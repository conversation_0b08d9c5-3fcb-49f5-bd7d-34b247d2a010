import React from "react";
import { StyleSheet, View } from "react-native";

// Components
import {
  Theme,
  useThemeAwareObject,
  SPACING,
  FONT_SIZES,
  CustomText,
  IconButton,
} from "b-ui-lib";

type Props = {
  titleBoldText: string;
  titleNormalText: string;
  handleCloseBottomSheet: () => void;
};

const BottomSheetHeader: React.FC = ({
  titleBoldText,
  titleNormalText,
  handleCloseBottomSheet,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <View style={styles.headerTitleContainer}>
        <CustomText style={[styles.text, { fontWeight: "700" }]}>
          {titleBoldText}
        </CustomText>
        <CustomText style={styles.text}>{titleNormalText}</CustomText>
      </View>

      <IconButton
        name="x"
        size={24}
        color={color.TEXT_GREY_DARKER}
        onPress={handleCloseBottomSheet}
      />
    </View>
  );
};

export default BottomSheetHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      justifyContent: "space-between",
      backgroundColor:"red"
    },
    headerTitleContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: SPACING.XXS,
    },
    text: {
      fontSize: FONT_SIZES.TWENTY,
      color: color.TEXT_GREY_DARKER,
    },
  });

  return { styles, color };
};
