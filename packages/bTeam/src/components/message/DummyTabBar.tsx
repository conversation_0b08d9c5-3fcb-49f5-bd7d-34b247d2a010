import React, { memo } from "react";
import { Pressable, View } from "react-native";
import { TabBarLabel, Theme, useThemeAwareObject } from "b-ui-lib";

type Props = {
  tabIcons: string[];
};

const DummyTabBar = memo<Props>(({ tabIcons }) => {
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));

  const activeTabStyle = {
    flex: 1,
    justifyContent: "center" as const,
    alignItems: "center" as const,
    height: 75,
    backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    borderWidth: 1,
    borderRightColor: color.BORDER_EMAIL_FOLDER,
    borderLeftColor: color.BORDER_EMAIL_FOLDER,
    borderBottomColor: color.MESSAGE_ITEM__BACKGROUND,
    borderTopWidth: 3,
    borderTopColor: color.MESSAGE_FLAG,
    elevation: 0,
    shadowOpacity: 0,
  };

  const inactiveTabStyle = {
    flex: 1,
    justifyContent: "center" as const,
    alignItems: "center" as const,
    height: 75,
    backgroundColor: color.BORDER_EMAIL_FOLDER,
    borderWidth: 1,
    borderRightColor: color.BORDER_EMAIL_FOLDER,
    borderLeftColor: color.BORDER_EMAIL_FOLDER,
    borderBottomColor: color.MESSAGE_ITEM__BACKGROUND,
    borderTopWidth: 3,
    borderTopColor: color.BORDER_EMAIL_FOLDER,
    elevation: 0,
    shadowOpacity: 0,
  };

  return (
    <View style={{ flexDirection: "row", backgroundColor: "#61616B" }}>
      <Pressable style={activeTabStyle}>
        <TabBarLabel iconName="mail" focused={true} onPress={() => {}} />
      </Pressable>
      {tabIcons.map((item, index) => (
        <Pressable key={`dummy-tab-${index}`} style={inactiveTabStyle}>
          <TabBarLabel
            iconName={item}
            focused={false}
            onPress={() => {}}
          />
        </Pressable>
      ))}
    </View>
  );
});

DummyTabBar.displayName = 'DummyTabBar';

export default DummyTabBar;