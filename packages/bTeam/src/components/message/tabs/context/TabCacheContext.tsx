import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface TabCacheItem {
  content: ReactNode;
  timestamp: number;
  props: any;
}

interface TabCacheState {
  [messageId: string]: {
    [tabIndex: number]: TabCacheItem;
  };
}

interface TabCacheContextType {
  getCachedTab: (messageId: string, tabIndex: number) => TabCacheItem | null;
  setCachedTab: (messageId: string, tabIndex: number, content: ReactNode, props: any) => void;
  clearTabCache: (messageId: string, tabIndex?: number) => void;
  clearAllCache: () => void;
  isCacheValid: (messageId: string, tabIndex: number, currentProps: any) => boolean;
}

const TabCacheContext = createContext<TabCacheContextType | null>(null);

const CACHE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes
const MAX_CACHE_SIZE = 50; // Maximum number of cached tabs across all messages

export const TabCacheProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [cache, setCache] = useState<TabCacheState>({});

  // Helper function to compare props for cache validity
  const propsEqual = (props1: any, props2: any) => {
    // Simple shallow comparison for basic props
    const keys1 = Object.keys(props1 || {});
    const keys2 = Object.keys(props2 || {});
    
    if (keys1.length !== keys2.length) return false;
    
    return keys1.every(key => {
      const val1 = props1[key];
      const val2 = props2[key];
      
      // For arrays, check length and first few items
      if (Array.isArray(val1) && Array.isArray(val2)) {
        return val1.length === val2.length && 
               val1.slice(0, 3).every((item, idx) => item === val2[idx]);
      }
      
      // For objects, do shallow comparison
      if (typeof val1 === 'object' && typeof val2 === 'object' && val1 && val2) {
        const objKeys1 = Object.keys(val1);
        const objKeys2 = Object.keys(val2);
        return objKeys1.length === objKeys2.length && 
               objKeys1.slice(0, 5).every(k => val1[k] === val2[k]);
      }
      
      return val1 === val2;
    });
  };

  const getCachedTab = useCallback((messageId: string, tabIndex: number): TabCacheItem | null => {
    const messageCache = cache[messageId];
    if (!messageCache) return null;
    
    const tabCache = messageCache[tabIndex];
    if (!tabCache) return null;
    
    // Check if cache is expired
    const now = Date.now();
    if (now - tabCache.timestamp > CACHE_EXPIRY_MS) {
      // Remove expired cache
      setCache(prev => {
        const newCache = { ...prev };
        delete newCache[messageId][tabIndex];
        if (Object.keys(newCache[messageId]).length === 0) {
          delete newCache[messageId];
        }
        return newCache;
      });
      return null;
    }
    
    return tabCache;
  }, [cache]);

  const setCachedTab = useCallback((messageId: string, tabIndex: number, content: ReactNode, props: any) => {
    setCache(prev => {
      const newCache = { ...prev };
      
      // Initialize message cache if it doesn't exist
      if (!newCache[messageId]) {
        newCache[messageId] = {};
      }
      
      // Add/update tab cache
      newCache[messageId][tabIndex] = {
        content,
        props: { ...props },
        timestamp: Date.now(),
      };
      
      // Cleanup old cache if we exceed max size
      const totalCacheItems = Object.values(newCache).reduce(
        (total, messageCache) => total + Object.keys(messageCache).length,
        0
      );
      
      if (totalCacheItems > MAX_CACHE_SIZE) {
        // Remove oldest cache items
        const allItems: Array<{ messageId: string; tabIndex: number; timestamp: number }> = [];
        Object.entries(newCache).forEach(([msgId, msgCache]) => {
          Object.entries(msgCache).forEach(([tabIdx, tabData]) => {
            allItems.push({
              messageId: msgId,
              tabIndex: parseInt(tabIdx),
              timestamp: tabData.timestamp,
            });
          });
        });
        
        // Sort by timestamp and remove oldest items
        allItems.sort((a, b) => a.timestamp - b.timestamp);
        const itemsToRemove = allItems.slice(0, totalCacheItems - MAX_CACHE_SIZE + 5); // Remove a few extra
        
        itemsToRemove.forEach(({ messageId: msgId, tabIndex: tabIdx }) => {
          delete newCache[msgId][tabIdx];
          if (Object.keys(newCache[msgId]).length === 0) {
            delete newCache[msgId];
          }
        });
      }
      
      return newCache;
    });
  }, []);

  const clearTabCache = useCallback((messageId: string, tabIndex?: number) => {
    setCache(prev => {
      const newCache = { ...prev };
      
      if (tabIndex !== undefined) {
        // Clear specific tab
        if (newCache[messageId]) {
          delete newCache[messageId][tabIndex];
          if (Object.keys(newCache[messageId]).length === 0) {
            delete newCache[messageId];
          }
        }
      } else {
        // Clear all tabs for message
        delete newCache[messageId];
      }
      
      return newCache;
    });
  }, []);

  const clearAllCache = useCallback(() => {
    setCache({});
  }, []);

  const isCacheValid = useCallback((messageId: string, tabIndex: number, currentProps: any): boolean => {
    const cachedTab = getCachedTab(messageId, tabIndex);
    if (!cachedTab) return false;
    
    return propsEqual(cachedTab.props, currentProps);
  }, [getCachedTab]);

  const value = {
    getCachedTab,
    setCachedTab,
    clearTabCache,
    clearAllCache,
    isCacheValid,
  };

  return (
    <TabCacheContext.Provider value={value}>
      {children}
    </TabCacheContext.Provider>
  );
};

export const useTabCache = () => {
  const context = useContext(TabCacheContext);
  if (!context) {
    throw new Error('useTabCache must be used within a TabCacheProvider');
  }
  return context;
};

export default TabCacheContext;